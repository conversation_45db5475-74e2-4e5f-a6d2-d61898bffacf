# LDIS Authentication System

This document describes the authentication system implemented for the Legal Document Issuance System (LDIS) settings page with admin mode functionality.

## Overview

The authentication system provides:
- **Settings page** with admin mode toggle
- **Single user registration** (only one user can sign up)
- **Secure authentication** with bcrypt password hashing
- **Password recovery** using recovery keys
- **Modal-based authentication** using Shadcn components

## Features

### 1. Settings Page (`/settings`)
- Clean, modern interface using Shadcn components
- Admin mode toggle switch
- Authentication required to enable admin mode
- Visual feedback when admin mode is active

### 2. Authentication Modal
- **Sign In**: Username and password authentication
- **Sign Up**: Create new user (only if no users exist)
- **Forgot Password**: Reset password using recovery key
- Tabbed interface for easy navigation
- Form validation and error handling
- Loading states and user feedback

### 3. Security Features
- Password hashing using bcrypt with 12 salt rounds
- Recovery key system for password reset
- Single user limitation (only one admin account)
- Input validation and sanitization
- Secure API endpoints

## File Structure

```
src/app/settings/page.tsx              # Settings page component
src/components/auth-dialog.tsx         # Authentication modal component
src/app/api/auth/signin/route.ts       # Sign in API endpoint
src/app/api/auth/signup/route.ts       # Sign up API endpoint
src/app/api/auth/reset-password/route.ts # Password reset API endpoint
scripts/test-auth.js                   # Authentication testing script
```

## API Endpoints

### POST /api/auth/signin
Authenticate existing user.

**Request Body:**
```json
{
  "username": "string",
  "password": "string"
}
```

**Response (Success):**
```json
{
  "message": "Sign in successful",
  "user": {
    "id": 1,
    "username": "admin",
    "created_at": "2025-07-30 18:02:43"
  }
}
```

### POST /api/auth/signup
Create new user (only if no users exist).

**Request Body:**
```json
{
  "username": "string",
  "password": "string",
  "recoveryKey": "string"
}
```

**Response (Success):**
```json
{
  "message": "User created successfully",
  "user": {
    "id": 1,
    "username": "admin"
  }
}
```

### POST /api/auth/reset-password
Reset password using recovery key.

**Request Body:**
```json
{
  "username": "string",
  "recoveryKey": "string",
  "newPassword": "string"
}
```

**Response (Success):**
```json
{
  "message": "Password reset successful",
  "user": {
    "id": 1,
    "username": "admin"
  }
}
```

## Usage

### 1. Accessing Settings
Navigate to `/settings` or click the Settings link in the sidebar.

### 2. Enabling Admin Mode
1. Toggle the "Admin Mode" switch
2. If not authenticated, the authentication modal will appear
3. Choose from Sign In, Sign Up, or Forgot Password tabs
4. Complete the authentication process
5. Admin mode will be enabled upon successful authentication

### 3. First Time Setup
1. Go to Settings page
2. Toggle Admin Mode
3. Use the "Sign Up" tab to create the first (and only) admin account
4. Provide username, password, and recovery key
5. Admin mode will be automatically enabled

### 4. Password Recovery
1. Go to Settings page
2. Toggle Admin Mode
3. Use the "Forgot Password" tab
4. Enter username and recovery key
5. Set new password
6. Use "Sign In" tab with new credentials

## Validation Rules

### Password Requirements
- Minimum 6 characters
- Required for both sign up and password reset

### Recovery Key Requirements
- Minimum 4 characters
- Required for sign up
- Used for password recovery

### Username Requirements
- Required field
- Must be unique (though only one user allowed)

## Testing

### Run Authentication Tests
```bash
pnpm run auth:test
```

This will test:
1. User sign up functionality
2. Sign in authentication
3. Password reset process
4. Single user limitation enforcement

### Manual Testing
1. Start the development server: `pnpm dev`
2. Navigate to `http://localhost:3000/settings`
3. Test the admin mode toggle and authentication flow

## Security Considerations

1. **Password Security**: All passwords are hashed using bcrypt with 12 salt rounds
2. **Single User**: Only one admin account can be created
3. **Recovery Keys**: Stored in plain text for password recovery (consider encryption for production)
4. **Input Validation**: All inputs are validated on both client and server side
5. **Error Handling**: Generic error messages to prevent information disclosure

## Database Schema

The authentication system uses the existing `users` table:

```sql
CREATE TABLE users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT UNIQUE NOT NULL,
  password TEXT NOT NULL,
  recovery_key TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Components Used

### Shadcn Components
- `Dialog` - Authentication modal
- `Tabs` - Authentication form tabs
- `Switch` - Admin mode toggle
- `Card` - Settings page layout
- `Button` - Form actions
- `Input` - Form fields
- `Label` - Form labels
- `Alert` - Error messages

### Icons (Lucide React)
- `Loader2` - Loading spinner
- `AlertCircle` - Error indicator

## Error Handling

The system provides comprehensive error handling:
- Network errors
- Validation errors
- Authentication failures
- Database errors
- User-friendly error messages

## Future Enhancements

Potential improvements for production use:
1. Session management with JWT tokens
2. Password strength requirements
3. Account lockout after failed attempts
4. Encrypted recovery keys
5. Audit logging
6. Multi-factor authentication
7. Role-based access control
