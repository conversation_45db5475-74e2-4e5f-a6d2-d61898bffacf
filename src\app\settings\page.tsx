"use client";

import { useState } from "react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AuthDialog } from "@/components/auth-dialog";

export default function SettingsPage() {
  const [adminMode, setAdminMode] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [showAuthDialog, setShowAuthDialog] = useState(false);

  const handleAdminModeToggle = (checked: boolean) => {
    if (checked && !isAuthenticated) {
      // Show authentication dialog when trying to enable admin mode
      setShowAuthDialog(true);
    } else if (!checked) {
      // Allow disabling admin mode without authentication
      setAdminMode(false);
    } else {
      // User is already authenticated, toggle admin mode
      setAdminMode(checked);
    }
  };

  const handleAuthSuccess = () => {
    setIsAuthenticated(true);
    setAdminMode(true);
    setShowAuthDialog(false);
  };

  const handleAuthCancel = () => {
    setShowAuthDialog(false);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">
          Manage your application settings and preferences.
        </p>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Admin Mode</CardTitle>
            <CardDescription>
              Enable admin mode to access administrative features and controls.
              Authentication is required to enable this mode.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Switch
                id="admin-mode"
                checked={adminMode}
                onCheckedChange={handleAdminModeToggle}
              />
              <Label htmlFor="admin-mode">
                {adminMode ? "Admin mode enabled" : "Enable admin mode"}
              </Label>
            </div>
            {adminMode && (
              <div className="mt-4 p-4 bg-primary/10 rounded-lg">
                <p className="text-sm text-primary font-medium">
                  🔒 Admin mode is active
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  You now have access to administrative features.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Additional settings can be added here */}
        <Card>
          <CardHeader>
            <CardTitle>General Settings</CardTitle>
            <CardDescription>
              Configure general application settings.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Additional settings will be available here.
            </p>
          </CardContent>
        </Card>
      </div>

      <AuthDialog
        open={showAuthDialog}
        onClose={handleAuthCancel}
        onSuccess={handleAuthSuccess}
      />
    </div>
  );
}
