# LDIS SQLite Database Setup

This document describes the SQLite database setup for the Legal Document Issuance System (LDIS) Next.js project.

## Overview

The database system provides:
- SQLite database with automatic initialization
- User management with secure password hashing
- Auto-creation of tables when the database is first accessed
- Type-safe database operations
- Next.js API route integration

## Database Schema

### Users Table

```sql
CREATE TABLE users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT UNIQUE NOT NULL,
  password TEXT NOT NULL,
  recovery_key TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Files Structure

```
src/lib/database.ts          # Main database utility module
src/app/api/users/route.ts   # Example API routes for user management
scripts/init-db.js           # Database initialization script
scripts/test-db.js           # Database testing script
ldis.db                      # SQLite database file (auto-created)
```

## Installation

The required dependencies are already installed:
- `sqlite3` - SQLite database driver
- `bcryptjs` - Password hashing library
- `ts-node` - TypeScript execution for scripts

## Usage

### 1. Database Initialization

The database initializes automatically when first accessed. You can also manually initialize it:

```bash
node scripts/init-db.js
```

### 2. Using Database Functions

```typescript
import { 
  initializeDatabase,
  createUser,
  getUserByUsername,
  getAllUsers,
  updateUserPassword
} from '@/lib/database';

// Initialize database (optional - happens automatically)
await initializeDatabase();

// Create a new user
const userId = await createUser('john_doe', hashedPassword, 'recovery_key_123');

// Get user by username
const user = await getUserByUsername('john_doe');

// Get all users
const users = await getAllUsers();
```

### 3. API Routes Example

The system includes example API routes at `/api/users`:

- `GET /api/users` - Get all users (passwords excluded)
- `POST /api/users` - Create a new user

Example usage:

```javascript
// Create a new user
const response = await fetch('/api/users', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    username: 'john_doe',
    password: 'securePassword123',
    recoveryKey: 'optional_recovery_key'
  })
});

// Get all users
const users = await fetch('/api/users').then(res => res.json());
```

### 4. Password Security

Passwords are automatically hashed using bcryptjs with a salt rounds of 12:

```typescript
import bcrypt from 'bcryptjs';

// Hash password before storing
const hashedPassword = await bcrypt.hash(plainPassword, 12);

// Verify password
const isValid = await bcrypt.compare(plainPassword, hashedPassword);
```

## Available Database Functions

### Core Functions
- `initializeDatabase()` - Initialize database connection and create tables
- `getDatabase()` - Get database instance
- `closeDatabase()` - Close database connection
- `runQuery(sql, params)` - Execute a query
- `getRow(sql, params)` - Get single row
- `getAllRows(sql, params)` - Get all rows

### User Management Functions
- `createUser(username, hashedPassword, recoveryKey?)` - Create new user
- `getUserByUsername(username)` - Get user by username
- `getUserById(id)` - Get user by ID
- `updateUserPassword(id, hashedPassword)` - Update user password
- `updateUserRecoveryKey(id, recoveryKey)` - Update recovery key
- `getAllUsers()` - Get all users
- `deleteUser(id)` - Delete user

## Testing

Run the database test script to verify everything works:

```bash
node scripts/test-db.js
```

This will:
1. Initialize the database
2. Create a test user
3. Retrieve and verify the user
4. Test password hashing/verification
5. Display all users

## Database Location

The SQLite database file is created at: `./ldis.db` (in the project root)

## Next.js Integration

The database automatically initializes when imported in a Next.js environment. The initialization only happens on the server-side to avoid issues with client-side rendering.

## Error Handling

All database functions include proper error handling and logging. Errors are logged to the console and thrown as JavaScript Error objects.

## Security Considerations

1. **Password Hashing**: All passwords are hashed using bcryptjs with salt rounds of 12
2. **SQL Injection Prevention**: All queries use parameterized statements
3. **API Security**: The API routes exclude sensitive data (passwords, recovery keys) from responses
4. **Database File**: Ensure the database file is not accessible via web server

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure the application has write permissions to the project directory
2. **Database Locked**: Close any existing database connections before running scripts
3. **Module Not Found**: Ensure all dependencies are installed with `pnpm install`

### Debugging

Enable SQLite verbose mode by modifying the database.ts file:
```typescript
const sqlite = sqlite3.verbose(); // Already enabled
```

This will provide detailed logging of all database operations.
